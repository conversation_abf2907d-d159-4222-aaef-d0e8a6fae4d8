// Full-width, no border, no curve, for page headers
export function Title({ text1, text2 }) {
    return (
        <div className="w-screen left-0 right-0 flex gap-2 items-center mb-3 justify-center bg-[#2b5175] text-2xl font-semibold py-4"
            style={{ position: 'relative', marginLeft: 'calc(50% - 50vw)', marginRight: 'calc(50% - 50vw)' }}>
                <p className="text-white">
                    {text1}
                    <span className="text-white">{text2}</span>
                </p>
        </div>
    );
}

// Section title, with border and divider, for inside cards/sections
export function OtherTitle({text1 , text2}) {
    
    return (
        <div className="inline-flex gap-2 items-center mb-3 w-full justify-center bg-[#2b5175] border-solid border-2 border-[--textColor1] rounded-b-2xl text-2xl font-semibold py-4 dark:bg-white dark:border-[#024282]">
            <p className="text-white dark:text-[#2b5175]">
                {text1}
                <span className="text-white dark:text-[#2b5175]">
                    {text2}
                </span>
            </p>
        </div>

    )
}
