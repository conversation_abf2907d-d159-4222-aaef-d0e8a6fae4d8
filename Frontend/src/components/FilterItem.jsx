import { useState, useRef, useEffect } from 'react';

const FilterItem = ({ 
  label, 
  value, 
  checked, 
  onChange, 
  children,
  level = 0
}) => {
  const [height, setHeight] = useState(0);
  const childRef = useRef(null);

  useEffect(() => {
    if (childRef.current) {
      setHeight(checked ? childRef.current.scrollHeight : 0);
    }
  }, [checked, children]);

  // Different styles for different nesting levels
  const levelStyles = [
    "px-4 py-3 hover:bg-[#263D54]/10", // Level 0
    "pl-8 pr-4 py-2 hover:bg-[#263D54]/15", // Level 1
    "pl-12 pr-4 py-1.5 hover:bg-[#263D54]/20" // Level 2
  ];

  return (
    <label className={`flex flex-col gap-1 cursor-pointer w-full rounded-lg transition-all duration-300 ${levelStyles[level]}`}>
      <span className="flex gap-2 items-center">
        <input
          type="checkbox"
          className="w-4 h-4 accent-[#263D54] border-gray-300 rounded focus:ring-2 focus:ring-[#263D54] transition"
          value={value}
          checked={checked}
          onChange={onChange}
        />
        <span className="transition-colors duration-200 text-[#263D54] dark:text-white font-medium">
          {label}
        </span>
      </span>
      
      {children && (
        <div
          ref={childRef}
          className="overflow-hidden transition-all duration-300 ease-in-out"
          style={{ height: `${height}px` }}
        >
          <div className="mt-2 rounded-lg bg-[#263D54]/5 dark:bg-[#263D54]/10 border border-[#e3eaf3] dark:border-[#024282]">
            {children}
          </div>
        </div>
      )}
    </label>
  );
};

export default FilterItem;