import { useContext } from 'react';
import { ShopContext } from '../../context/ShopContextProvider';

const AdminWelcome = () => {
    const { user } = useContext(ShopContext);

    return (
        <div className="text-center mt-10 p-8">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-slate-700 dark:to-slate-800 rounded-2xl p-8 mb-8 shadow-xl">
                <h1 className="text-3xl font-bold text-white mb-4">
                    🎛️ Welcome to Admin Dashboard
                </h1>
                <p className="text-blue-100 dark:text-gray-300 text-lg">
                    Hello {user?.name || 'Admin'}! You have full access to manage the platform.
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">📦</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        Manage Products
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        Add, edit, and manage your product catalog
                    </p>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">📋</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        View Orders
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        Monitor and manage customer orders
                    </p>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">👥</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        Manage Users
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        View and manage user accounts
                    </p>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">🎫</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        Coupons
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        Create and manage discount coupons
                    </p>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">📊</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        Analytics
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        View sales and performance metrics
                    </p>
                </div>

                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-600">
                    <div className="text-4xl mb-4">⚙️</div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
                        Settings
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                        Configure platform settings
                    </p>
                </div>
            </div>

            <div className="mt-12 p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl">
                <div className="flex items-center justify-center mb-4">
                    <span className="text-2xl mr-2">🔒</span>
                    <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                        Admin-Only Access
                    </h3>
                </div>
                <p className="text-yellow-700 dark:text-yellow-300">
                    You are currently in admin mode. The main website is not accessible while you're logged in as an admin. 
                    Use the navigation sidebar to access different admin functions.
                </p>
            </div>
        </div>
    );
};

export default AdminWelcome;
