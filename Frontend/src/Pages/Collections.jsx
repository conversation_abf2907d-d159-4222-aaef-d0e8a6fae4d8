import { useContext, useEffect, useState } from "react";
import { ShopContext } from "../context/ShopContextProvider";
import { IoIosArrowForward } from "react-icons/io";
import { Title } from '../components/Title';
import ProductItem from "../components/ProductItem";
import { FaAngleLeft, FaAngleRight } from "react-icons/fa";
import NestedFilterComponent from "../components/NestedFilterComponent";

export default function Collections() {
    const { products, paginationResult, getProductsData } = useContext(ShopContext);
    const [showFilter, setShowFilter] = useState(false);
    const [filters, setFilters] = useState({
        category: [], // e.g., ["Medical"] or ["Geometric"]
        typecategory: [], // e.g., ["Clothes"] or ["Tools"]
        subcategory1: [], // e.g., ["White coat"] or ["Scrubs"]
        subcategory2: [] // e.g., ["Short Lab Coat"]
    });
    const [sortType, setSortType] = useState('relavent');
    const [keyword] = useState('');

    // Effect to fetch data when filters, keyword, or sort type changes
    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            try {
                let sortParam = '';
                if (sortType === 'low-high') {
                    sortParam = 'price';
                } else if (sortType === 'high-low') {
                    sortParam = '-price';
                }

                await getProductsData(
                    1, // Always start from the first page when filters change
                    20,
                    'name price imageCover ratingsAverage ratingsQuantity',
                    keyword,
                    filters,
                    sortParam
                );
            } finally {
                setIsLoading(false);
            }
        };
        fetchData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [keyword, filters, sortType]);

    // Effect to fetch data when only the page changes
    useEffect(() => {
        if (paginationResult?.currentPage) {
            const fetchPageData = async () => {
                setIsLoading(true);
                try {
                    let sortParam = '';
                    if (sortType === 'low-high') {
                        sortParam = 'price';
                    } else if (sortType === 'high-low') {
                        sortParam = '-price';
                    }

                    await getProductsData(
                        paginationResult.currentPage,
                        20,
                        'name price imageCover ratingsAverage ratingsQuantity',
                        keyword,
                        filters,
                        sortParam
                    );
                } finally {
                    setIsLoading(false);
                }
            };
            fetchPageData();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [paginationResult?.currentPage]);

    // Function to change page
    const paginate = async (page) => {
        if (!page || page < 1 || page > (paginationResult?.numberOfPages || 1)) return;

        let sortParam = '';
        if (sortType === 'low-high') {
            sortParam = 'price';
        } else if (sortType === 'high-low') {
            sortParam = '-price';
        }

        await getProductsData(
            page,
            20,
            'name price imageCover ratingsAverage ratingsQuantity',
            keyword,
            filters,
            sortParam
        );
    };



    return (
        <div className="px-4 sm:px-[5vw] md:px-[7vw] lg:px-[9vw]">
            <Title text1={'All'} text2={'Products'} />
            <div className="flex flex-col sm:flex-row gap-1 sm:gap-4 py-10 border-t border-slate-900 dark:border-[--textColor1]">
                {/* Filter Sidebar */}
                <div className="min-w-80">
                    <div className="sm:hidden">
                        <p className="my-2 text-xl font-bold flex items-center cursor-pointer gap-2" onClick={() => setShowFilter(!showFilter)}>
                            FILTERS
                            <IoIosArrowForward className={`w-4 h-4 text-[--textColor2] font-bold transition-all duration-500 ${showFilter ? 'rotate-90' : ''}`} />
                        </p>
                    </div>

                    <div className={`${showFilter ? '' : 'hidden'} sm:block`}>
                        <NestedFilterComponent
                            onFiltersChange={setFilters}
                            initialFilters={filters}
                        />
                    </div>
                </div>

                {/* Products Grid */}
                <div className="flex-1">
                    {/* Sort and View Options (e.g., Grid/List view, Sort by price/relevance) */}
                    <div className="flex flex-col sm:flex-row justify-between items-center py-4">
                        {/* Sort By */}
                        <div className="flex items-center gap-2">
                            <span className="text-sm">Sort by:</span>
                            <select
                                value={sortType}
                                onChange={(e) => setSortType(e.target.value)}
                                className="border border-slate-300 rounded-md p-2 text-sm"
                            >
                                <option value="relavent">Relevance</option>
                                <option value="low-high">Price: Low to High</option>
                                <option value="high-low">Price: High to Low</option>
                            </select>
                        </div>

                        {/* View Options (Grid/List) - Assuming you have a state for view type */}
                        <div className="flex items-center gap-2">
                            <span className="text-sm">View:</span>
                            <button className={`p-2 rounded-md ${viewType === 'grid' ? 'bg-blue-500 text-white' : 'bg-slate-100'}`} onClick={() => setViewType('grid')}>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h18M3 9h18M3 15h18M3 21h18" />
                                </svg>
                            </button>
                            <button className={`p-2 rounded-md ${viewType === 'list' ? 'bg-blue-500 text-white' : 'bg-slate-100'}`} onClick={() => setViewType('list')}>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7h18M3 12h18M3 17h18" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Products Grid/List - Assuming you have a component for rendering products */}
                    <div className={`grid ${viewType === 'grid' ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'} gap-4`}>
                        {products.map(product => (
                            <ProductItem key={product.id} product={product} />
                        ))}
                    </div>

                    {/* Pagination - Assuming you have a pagination component */}
                    <div className="flex justify-center py-4">
                        <button
                            onClick={() => paginate(paginationResult.currentPage - 1)}
                            disabled={paginationResult.currentPage === 1}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50"
                        >
                            <FaAngleLeft className="w-4 h-4" />
                        </button>
                        <span className="px-4 py-2 text-sm">
                            Page {paginationResult.currentPage} of {paginationResult.numberOfPages}
                        </span>
                        <button
                            onClick={() => paginate(paginationResult.currentPage + 1)}
                            disabled={paginationResult.currentPage === paginationResult.numberOfPages}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50"
                        >
                            <FaAngleRight className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}