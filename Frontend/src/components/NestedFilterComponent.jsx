import { useState, useEffect } from 'react';
import { NESTED_FILTER_STRUCTURE } from '../config/filtersConfig';
import { IoIosArrowDown, IoIosArrowForward } from 'react-icons/io';

const NestedFilterComponent = ({ onFiltersChange, initialFilters = {} }) => {
    const [expandedItems, setExpandedItems] = useState({});
    const [selectedFilters, setSelectedFilters] = useState(initialFilters);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    // Toggle expansion of filter sections
    const toggleExpansion = (path) => {
        setExpandedItems(prev => ({
            ...prev,
            [path]: !prev[path]
        }));
    };

    // Handle filter selection
    const handleFilterChange = (filterPath, value, isChecked) => {
        setSelectedFilters(prev => {
            const newFilters = { ...prev };
            
            if (!newFilters[filterPath]) {
                newFilters[filterPath] = [];
            }

            if (isChecked) {
                if (!newFilters[filterPath].includes(value)) {
                    newFilters[filterPath] = [...newFilters[filterPath], value];
                }
            } else {
                newFilters[filterPath] = newFilters[filterPath].filter(item => item !== value);
            }

            // Notify parent component of filter changes
            onFiltersChange && onFiltersChange(newFilters);
            return newFilters;
        });
    };

    // Check if a filter is selected
    const isFilterSelected = (filterPath, value) => {
        return selectedFilters[filterPath]?.includes(value) || false;
    };

    // Render filter details (level 4)
    const renderDetails = (details, parentPath, subcategoryValue) => {
        if (!details || details.length === 0) return null;

        return (
            <div className="ml-6 mt-2 space-y-1">
                {details.map((detail) => (
                    <label 
                        key={detail.value}
                        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-slate-700/50 cursor-pointer transition-all duration-200"
                    >
                        <input
                            type="checkbox"
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            checked={isFilterSelected('subcategory2', detail.value)}
                            onChange={(e) => handleFilterChange('subcategory2', detail.value, e.target.checked)}
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                            {detail.label}
                        </span>
                    </label>
                ))}
            </div>
        );
    };

    // Render subcategories (level 3)
    const renderSubcategories = (subcategories, parentPath, typeValue) => {
        if (!subcategories || subcategories.length === 0) return null;

        return (
            <div className="ml-4 mt-2 space-y-2">
                {subcategories.map((subcategory) => {
                    const subcategoryPath = `${parentPath}/subcategories/${subcategory.value}`;
                    const isExpanded = expandedItems[subcategoryPath];
                    const isSelected = isFilterSelected('subcategory1', subcategory.value);

                    return (
                        <div key={subcategory.value} className="border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-800 shadow-sm">
                            <div className="p-3">
                                <div className="flex items-center justify-between">
                                    <label className="flex items-center space-x-3 cursor-pointer flex-1">
                                        <input
                                            type="checkbox"
                                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            checked={isSelected}
                                            onChange={(e) => handleFilterChange('subcategory1', subcategory.value, e.target.checked)}
                                        />
                                        <span className="text-lg">{subcategory.icon}</span>
                                        <span className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                            {subcategory.label}
                                        </span>
                                    </label>
                                    {subcategory.details && subcategory.details.length > 0 && (
                                        <button
                                            onClick={() => toggleExpansion(subcategoryPath)}
                                            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200"
                                        >
                                            {isExpanded ? (
                                                <IoIosArrowDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                            ) : (
                                                <IoIosArrowForward className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                            )}
                                        </button>
                                    )}
                                </div>
                                
                                {/* Details section */}
                                {isExpanded && subcategory.details && (
                                    <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                                        {renderDetails(subcategory.details, subcategoryPath, subcategory.value)}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
        );
    };

    // Render types (level 2)
    const renderTypes = (types, parentPath, categoryValue) => {
        if (!types || types.length === 0) return null;

        return (
            <div className="ml-2 mt-3 space-y-3">
                {types.map((type) => {
                    const typePath = `${parentPath}/types/${type.value}`;
                    const isExpanded = expandedItems[typePath];
                    const isSelected = isFilterSelected('typecategory', type.value);

                    return (
                        <div key={type.value} className="border border-gray-300 dark:border-gray-600 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 shadow-md">
                            <div className="p-4">
                                <div className="flex items-center justify-between">
                                    <label className="flex items-center space-x-3 cursor-pointer flex-1">
                                        <input
                                            type="checkbox"
                                            className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            checked={isSelected}
                                            onChange={(e) => handleFilterChange('typecategory', type.value, e.target.checked)}
                                        />
                                        <span className="text-xl">{type.icon}</span>
                                        <span className="text-base font-bold text-gray-900 dark:text-gray-100">
                                            {type.label}
                                        </span>
                                    </label>
                                    {type.subcategories && type.subcategories.length > 0 && (
                                        <button
                                            onClick={() => toggleExpansion(typePath)}
                                            className="p-2 rounded-full hover:bg-white/50 dark:hover:bg-slate-600/50 transition-colors duration-200"
                                        >
                                            {isExpanded ? (
                                                <IoIosArrowDown className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                                            ) : (
                                                <IoIosArrowForward className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                                            )}
                                        </button>
                                    )}
                                </div>
                                
                                {/* Subcategories section */}
                                {isExpanded && type.subcategories && (
                                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                                        {renderSubcategories(type.subcategories, typePath, type.value)}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
        );
    };

    // Render main categories (level 1)
    const renderCategories = () => {
        return NESTED_FILTER_STRUCTURE.categories.map((category) => {
            const categoryPath = `categories/${category.value}`;
            const isExpanded = expandedItems[categoryPath];
            const isSelected = isFilterSelected('category', category.value);

            return (
                <div key={category.value} className="border-2 border-gray-400 dark:border-gray-500 rounded-2xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 shadow-lg mb-4">
                    <div className="p-5">
                        <div className="flex items-center justify-between">
                            <label className="flex items-center space-x-4 cursor-pointer flex-1">
                                <input
                                    type="checkbox"
                                    className="w-6 h-6 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    checked={isSelected}
                                    onChange={(e) => handleFilterChange('category', category.value, e.target.checked)}
                                />
                                <span className="text-2xl">{category.icon}</span>
                                <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                    {category.label}
                                </span>
                            </label>
                            {category.types && category.types.length > 0 && (
                                <button
                                    onClick={() => toggleExpansion(categoryPath)}
                                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-200"
                                >
                                    {isExpanded ? (
                                        <IoIosArrowDown className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                                    ) : (
                                        <IoIosArrowForward className="w-6 h-6 text-gray-700 dark:text-gray-300" />
                                    )}
                                </button>
                            )}
                        </div>
                        
                        {/* Types section */}
                        {isExpanded && category.types && (
                            <div className="mt-5 pt-5 border-t-2 border-gray-200 dark:border-gray-600">
                                {renderTypes(category.types, categoryPath, category.value)}
                            </div>
                        )}
                    </div>
                </div>
            );
        });
    };

    return (
        <div className={`
            w-full transition-all duration-500 ease-out
            ${isMounted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}
        `}>
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-slate-700 dark:to-slate-800 rounded-2xl p-6 mb-6 shadow-xl">
                <h2 className="text-xl font-bold text-white dark:text-gray-100 flex items-center space-x-2">
                    <span className="text-2xl">🔍</span>
                    <span>{NESTED_FILTER_STRUCTURE.title}</span>
                </h2>
                <p className="text-blue-100 dark:text-gray-300 mt-2 text-sm">
                    Select categories and subcategories to filter products
                </p>
            </div>
            
            <div className="space-y-4">
                {renderCategories()}
            </div>
        </div>
    );
};

export default NestedFilterComponent;
