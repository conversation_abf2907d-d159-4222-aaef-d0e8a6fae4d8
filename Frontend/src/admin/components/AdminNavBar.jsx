import { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { graduations } from '../../graduation_project/graduations';
import { ShopContext } from '../../context/ShopContextProvider';

export default function AdminNavBar() {
    const navigate = useNavigate();
    const { logout, isDark, setIsDark } = useContext(ShopContext);

    const handleLogoClick = () => {
        navigate('/');
    };

    const handleLogout = async () => {
        await logout();
        navigate('/Login');
    };

    return (
        <div className='flex items-center justify-between py-5 font-medium px-4 sm:px-[5vw] md:px-[7vw] lg:px-[9vw] bg-[--color1] dark:bg-slate-800 relative transition-all duration-300'> {/* Added shadow for depth */}
            {/* Logo with Animation and Click Handler */}
            <img
                className='w-[max(10%,80px)] cursor-pointer transform transition-all duration-300 ease-in-out hover:scale-105 hover:shadow- active:scale-95 active:rotate-0'
                src={graduations.logo2}
                alt="Logo"
                onClick={handleLogoClick}
            />

            {/* Dark Mode Toggle and Logout Button */}
            <div className='flex items-center gap-4'>
                {/* Dark Mode Toggle */}
                <label className="switch">
                    <input
                        defaultChecked={isDark}
                        type="checkbox"
                        onChange={() => setIsDark(!isDark)}
                    />
                    <span className="slider">
                        <div className="star star_1" />
                        <div className="star star_2" />
                        <div className="star star_3" />
                        <svg viewBox="0 0 16 16" className="cloud_1 cloud">
                            <path transform="matrix(.77976 0 0 .78395-299.99-418.63)" fill="#fff" d="m391.84 540.91c-.421-.329-.949-.524-1.523-.524-1.351 0-2.451 1.084-2.485 2.435-1.395.526-2.388 1.88-2.388 3.466 0 1.874 1.385 3.423 3.182 3.667v.034h12.73v-.006c1.775-.104 3.182-1.584 3.182-3.395 0-1.747-1.309-3.186-2.994-3.379.007-.106.011-.214.011-.322 0-2.707-2.271-4.901-5.072-4.901-2.073 0-3.856 1.202-4.643 2.925" />
                        </svg>
                    </span>
                </label>

                {/* Logout Button with Animation */}
                <button
                    onClick={handleLogout}
                    className='bg-[#2b5175] dark:bg-slate-600 text-white px-5 py-2 sm:px-7 sm:py-2 rounded-full text-xs sm:text-sm
                               transform transition-all duration-300 ease-in-out
                               hover:scale-105 hover:shadow-md hover:bg-opacity-90 dark:hover:bg-slate-500
                               active:scale-95 active:bg-opacity-100'
                >
                    Logout
                </button>
            </div>
        </div>
    );
}