import { useState, useEffect } from 'react';
import FilterItem from './FilterItem';
import { FILTER_CATEGORIES, SUBFILTERS_LEVEL2 } from '../filtersConfig';

const FilterSection = ({ initialCategoryKey }) => {
  const [isMounted, setIsMounted] = useState(false);
  const [expandedItems, setExpandedItems] = useState({});
  const [checkedItems, setCheckedItems] = useState({});

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const categoryData = FILTER_CATEGORIES[initialCategoryKey];
  if (!categoryData) return null;

  const handleCheckChange = (path, value, isChecked) => {
    setCheckedItems(prev => {
      const newState = { ...prev };
      
      // Update the checked state
      newState[path] = newState[path] || {};
      newState[path][value] = isChecked;
      
      // Uncheck children when parent is unchecked
      if (!isChecked) {
        Object.keys(newState).forEach(key => {
          if (key.startsWith(`${path}/${value}/`)) {
            delete newState[key];
          }
        });
      }
      
      return newState;
    });
    
    // Expand when checked
    if (isChecked) {
      setExpandedItems(prev => ({
        ...prev,
        [path]: { ...prev[path], [value]: true }
      }));
    }
  };

  const renderSubFilters = (parentPath, items, level = 1) => {
    return items.map(item => {
      const itemPath = `${parentPath}/${item.value}`;
      const isChecked = checkedItems[parentPath]?.[item.value] || false;
      
      return (
        <FilterItem
          key={item.value}
          label={item.label}
          value={item.value}
          level={level}
          checked={isChecked}
          onChange={(e) => handleCheckChange(parentPath, item.value, e.target.checked)}
        >
          {SUBFILTERS_LEVEL2[item.value] && (
            <div className="pl-1 py-1">
              {renderSubFilters(itemPath, SUBFILTERS_LEVEL2[item.value], level + 1)}
            </div>
          )}
        </FilterItem>
      );
    });
  };

  return (
    <div className={`
      border border-[#263D54] bg-[#f7fafd] dark:bg-[#263D54]/10
      w-full rounded-xl px-2 py-4 my-5
      text-[#263D54] dark:text-white
      shadow transition-all duration-300 ease-[cubic-bezier(0.34,1.56,0.64,1)]
      transform ${isMounted ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
    `}>
      <p className="mb-3 px-4 text-base font-bold tracking-wide">{categoryData.title}</p>
      <div className="flex flex-col text-sm font-normal">
        {categoryData.options.map(option => {
          const optionPath = initialCategoryKey;
          const isChecked = checkedItems[optionPath]?.[option.value] || false;
          
          return (
            <FilterItem
              key={option.value}
              label={option.label}
              value={option.value}
              checked={isChecked}
              onChange={(e) => handleCheckChange(optionPath, option.value, e.target.checked)}
            >
              {FILTER_CATEGORIES.typecategory.subFilters?.[option.value]?.Medical && (
                <div className="pl-1 py-1">
                  <p className="text-sm font-semibold mb-1 px-4 mt-1 text-[#263D54] dark:text-white">
                    Medical
                  </p>
                  {renderSubFilters(
                    `${optionPath}/${option.value}/Medical`, 
                    FILTER_CATEGORIES.typecategory.subFilters[option.value].Medical
                  )}
                  
                  <p className="text-sm font-semibold mb-1 px-4 mt-3 text-[#263D54] dark:text-white">
                    Geometric
                  </p>
                  {renderSubFilters(
                    `${optionPath}/${option.value}/Geometric`, 
                    FILTER_CATEGORIES.typecategory.subFilters[option.value].Geometric
                  )}
                </div>
              )}
            </FilterItem>
          );
        })}
      </div>
    </div>
  );
};

export default FilterSection;