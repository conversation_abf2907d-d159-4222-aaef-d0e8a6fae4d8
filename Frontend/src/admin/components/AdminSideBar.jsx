import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { assets } from '../assets/assets';

export default function AdminSideBar() {
    const location = useLocation();

    // Determine which link should be active based on the current pathname
    const getActive = (key) => {
        if (location.pathname === '/admin/') return false; // Unactive all on dashboard
        if (key === 'add' && location.pathname.startsWith('/admin/add')) return true;
        if (key === 'list' && location.pathname.startsWith('/admin/list')) return true;
        if (key === 'orders' && location.pathname.startsWith('/admin/orders')) return true;
        return false;
    };

    return (
        <div className='w-full md:w-[18%] min-h-screen border-r border-gray-700 bg-[--color1] text-white flex flex-col justify-between'>
            {/* Top section: Navigation Links */}
            <div className='flex flex-col gap-2 pt-6 px-4 md:pl-[20%]'> {/* Adjusted padding for better responsiveness */}

                {/* NavLink for Add Items */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('add')
                            ? 'bg-[#2b5175] text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/add'}
                >
                    {/* Hover effect background */}
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.add_icon} alt="" />
                    <p className='hidden md:block font-medium'>Add Items</p>
                </NavLink>

                {/* NavLink for List Items */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('list')
                            ? 'bg-[#2b5175] text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/list'}
                >
                     <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.order_icon} alt="" />
                    <p className='hidden md:block font-medium'>List Items</p>
                </NavLink>

                {/* NavLink for Orders */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('orders')
                            ? 'bg-[#1B5CBECC] text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/orders'}
                >
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.order_icon} alt="" />
                    <p className='hidden md:block font-medium'>Orders</p>
                </NavLink>
            </div>
        </div>
    );
}