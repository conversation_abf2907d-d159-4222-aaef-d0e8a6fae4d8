import axios from 'axios'
import React, { useEffect, useState, useContext } from 'react'
import { toast } from 'react-toastify'
import { ShopContext } from '../../context/ShopContextProvider'
import { Link } from 'react-router-dom'

export default function List() {
    const { backendUrl, currency, token } = useContext(ShopContext)
    const [list , setList] = useState([])
    async function fetchList() {
        try {
            const response = await axios.get(backendUrl + '/api/v1/products')
            console.log('response of the admin list>>>',response.data.data);
            if(response){
                setList(response.data.data);
            } else {
                toast.error(response.data.message)
            }
        } catch (error) {
            console.log(error);
            toast.error(error.message)
        }
    }

    async function removeProduct(id) {
        try {
            const response = await axios.delete(
                `${backendUrl}/api/v1/products/${id}`,
                { headers: { Authorization: `Bearer ${token}` } }
            );
            console.log('response of remove product>>>', response);
            if (response) {
                toast.success(response.data.message);
                await fetchList();
            } else {
                toast.error(response.data.message);
            }
        } catch (error) {
            console.log(error);
            toast.error(error.message);
        }
    }

    useEffect(()=>{
        fetchList()
    },[])
    return (
        <div className="max-w-6xl mx-auto p-4">
            <h2 className="text-2xl font-bold mb-4 text-[#263D54] dark:text-white">All Products</h2>
            <div className="overflow-x-auto rounded-xl shadow border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-800">
                <table className="min-w-full text-sm">
                    <thead>
                        <tr className="bg-[#263D54] text-white">
                            <th className="py-3 px-2 rounded-tl-xl">Image</th>
                            <th className="py-3 px-2">Name</th>
                            <th className="py-3 px-2">Category</th>
                            <th className="py-3 px-2">Type</th>
                            <th className="py-3 px-2">Subcategory</th>
                            <th className="py-3 px-2">Price</th>
                            <th className="py-3 px-2 rounded-tr-xl text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {list.length === 0 ? (
                            <tr>
                                <td colSpan={7} className="text-center py-8 text-gray-400">No products found.</td>
                            </tr>
                        ) : (
                            [...list].reverse().map((item, index) => (
                                <tr key={item._id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-slate-700 transition">
                                    <td className="py-2 px-2">
                                        <img
                                            className="w-16 h-16 object-cover rounded-lg border border-gray-200 shadow-sm"
                                            src={item.imageCover}
                                            alt={item.name}
                                        />
                                    </td>
                                    <td className="py-2 px-2 font-semibold">{item.name}</td>
                                    <td className="py-2 px-2">{item.category}</td>
                                    <td className="py-2 px-2">{item.typecategory}</td>
                                    <td className="py-2 px-2">{item.subcategory1}</td>
                                    <td className="py-2 px-2">{item.price} {currency}</td>
                                    <td className="py-2 px-2 flex gap-2 justify-center">
                                        <Link
                                            to={`/admin/edit/${item._id}`}
                                            className="px-4 py-1 rounded-full bg-[#2b5175] text-white hover:bg-blue-600 transition font-semibold shadow"
                                        >
                                            Edit
                                        </Link>
                                        <button
                                            onClick={() => removeProduct(item._id)}
                                            className="px-4 py-1 rounded-full bg-red-500 text-white hover:bg-red-600 transition font-semibold shadow"
                                        >
                                            Delete
                                        </button>
                                    </td>
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    )
}
