import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { graduations } from '../../graduation_project/graduations';
import { ShopContext } from '../../context/ShopContextProvider';

export default function AdminNavBar() {
    const navigate = useNavigate();
    const { logout } = useContext(ShopContext);

    const handleLogoClick = () => {
        navigate('/');
    };

    const handleLogout = () => {
        logout();
        navigate('/Login');
    };

    return (
        <div className='flex items-center py-2 px-[4%] justify-between bg-[--color1] shadow-lg'> {/* Added shadow for depth */}
            {/* Logo with Animation and Click Handler */}
            <img
                className='w-[max(10%,80px)] cursor-pointer transform transition-all duration-300 ease-in-out hover:scale-105 hover:shadow- active:scale-95 active:rotate-0'
                src={graduations.logo2}
                alt="Logo"
                onClick={handleLogoClick}
            />
            
            {/* <PERSON>go<PERSON> with Animation */}
            <button
                onClick={handleLogout}
                className='bg-[#2b5175] text-white px-5 py-2 sm:px-7 sm:py-2 rounded-full text-xs sm:text-sm
                           transform transition-all duration-300 ease-in-out
                           hover:scale-105 hover:shadow-md hover:bg-opacity-90
                           active:scale-95 active:bg-opacity-100'
            >
                Logout
            </button>
        </div>
    );
}