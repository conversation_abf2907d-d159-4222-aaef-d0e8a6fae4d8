import { useContext, useEffect, useState } from "react";
import { ShopContext } from "../context/ShopContextProvider";
import { IoIosArrowForward } from "react-icons/io";
import { Title } from '../components/Title';
import ProductItem from "../components/ProductItem";
import { FaAngleLeft, FaAngleRight } from "react-icons/fa";
import { FILTER_CATEGORIES, SUBFILTERS_LEVEL2 } from "../config/filtersConfig";
import { FilterSection, FilterItem } from "../components/FilterSection";
import { motion } from "motion/react"

export default function Collections() {
    const { products, paginationResult, getProductsData } = useContext(ShopContext);
    const [showFilter, setShowFilter] = useState(false);
    const [filters, setFilters] = useState({
        category: [], // e.g., ["Medical"] or ["Geometric"]
        typecategory: [], // e.g., ["Clothes"] or ["Tools"]
        subcategory1: [], // e.g., ["White coat"] or ["Scrubs"]
        subcategory2: [] // e.g., ["Short Lab Coat"]
    });
    const [sortType, setSortType] = useState('relavent');
    // eslint-disable-next-line no-unused-vars
    const [keyword, setKeyword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [activeFilters, setActiveFilters] = useState({
        category: [],
        typecategory: []
      });

    // Effect to fetch data when filters, keyword, or sort type changes
    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            try {
                let sortParam = '';
                if (sortType === 'low-high') {
                    sortParam = 'price';
                } else if (sortType === 'high-low') {
                    sortParam = '-price';
                }

                await getProductsData(
                    1, // Always start from the first page when filters change
                    20,
                    'name price imageCover ratingsAverage ratingsQuantity',
                    keyword,
                    filters,
                    sortParam
                );
            } finally {
                setIsLoading(false);
            }
        };
        fetchData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [keyword, filters, sortType]);

    // Effect to fetch data when only the page changes
    useEffect(() => {
        if (paginationResult?.currentPage) {
            const fetchPageData = async () => {
                setIsLoading(true);
                try {
                    let sortParam = '';
                    if (sortType === 'low-high') {
                        sortParam = 'price';
                    } else if (sortType === 'high-low') {
                        sortParam = '-price';
                    }

                    await getProductsData(
                        paginationResult.currentPage,
                        20,
                        'name price imageCover ratingsAverage ratingsQuantity',
                        keyword,
                        filters,
                        sortParam
                    );
                } finally {
                    setIsLoading(false);
                }
            };
            fetchPageData();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [paginationResult?.currentPage]);

    // Function to change page
    const paginate = async (page) => {
        if (!page || page < 1 || page > (paginationResult?.numberOfPages || 1)) return;

        let sortParam = '';
        if (sortType === 'low-high') {
            sortParam = 'price';
        } else if (sortType === 'high-low') {
            sortParam = '-price';
        }

        await getProductsData(
            page,
            20,
            'name price imageCover ratingsAverage ratingsQuantity',
            keyword,
            filters,
            sortParam
        );
    };

    // Function to update filters
    const updateFilter = (filterType, value) => {
        setFilters(prev => {
            const newFilters = { ...prev };

            if (newFilters[filterType].includes(value)) {
                // Remove value if already present
                newFilters[filterType] = newFilters[filterType].filter(item => item !== value);

                // Clear dependent filters when a parent filter is deselected
                if (filterType === 'category') {
                    newFilters.typecategory = [];
                    newFilters.subcategory1 = [];
                    newFilters.subcategory2 = [];
                } else if (filterType === 'typecategory') {
                    newFilters.subcategory1 = [];
                    newFilters.subcategory2 = [];
                } else if (filterType === 'subcategory1') {
                    newFilters.subcategory2 = [];
                }
            } else {
                // Add value if not present
                // Special handling for 'category' to allow only one selection
                if (filterType === 'category') {
                    newFilters[filterType] = [value]; // Only allow one category
                    // Also clear typecategory and subcategories when category changes
                    newFilters.typecategory = [];
                    newFilters.subcategory1 = [];
                    newFilters.subcategory2 = [];
                } else {
                    newFilters[filterType] = [...newFilters[filterType], value];
                }
            }
            return newFilters;
        });
    };

    // Helper to get selected category (Medical or Geometric) from the filters state
    const getSelectedCategory = () => {
        return filters.category.length > 0 ? filters.category[0] : null;
    };

    // Helper to get sub-filters for typecategory based on selected category
    const getTypeCategorySubFilters = (typeValue) => {
        const selectedCategory = getSelectedCategory();
        if (selectedCategory && FILTER_CATEGORIES.typecategory.subFilters?.[typeValue]?.[selectedCategory]) {
            return FILTER_CATEGORIES.typecategory.subFilters[typeValue][selectedCategory];
        }
        return [];
    };

    // Helper to get sub-filters for subcategory1 based on selected typecategory
    const getSubcategory1SubFilters = (subcat1Value) => {
        return SUBFILTERS_LEVEL2[subcat1Value] || [];
    };

    return (
        <div className="px-4 sm:px-[5vw] md:px-[7vw] lg:px-[9vw]">
            <Title text1={'All'} text2={'Products'} />
            <div className="flex flex-col sm:flex-row gap-1 sm:gap-4 py-10 border-t border-slate-900 dark:border-[--textColor1]">
                {/* Filter Sidebar */}
                <div className="min-w-52">
                    <p className="my-2 text-xl font-bold flex items-center cursor-pointer gap-2" onClick={() => setShowFilter(!showFilter)}>
                        FILTERS
                        <IoIosArrowForward className={`w-4 h-4 text-[--textColor2] font-bold sm:hidden transition-all duration-500 ${showFilter ? 'rotate-90' : ''}`} />
                    </p>

                    <div className={`${showFilter ? '' : 'hidden'} sm:block`}>
                        {/* Category Filter */}
                        <FilterSection title={FILTER_CATEGORIES.category.title}>
                            {FILTER_CATEGORIES.category.options.map(option => (
                                <FilterItem
                                    key={option.value}
                                    label={option.label}
                                    value={option.value}
                                    checked={filters.category.includes(option.value)}
                                    onChange={() => updateFilter('category', option.value)}
                                />
                            ))}
                        </FilterSection>

                        {/* Type Category Filter (e.g., Clothes, Tools) */}
                        {filters.category.length > 0 && ( // Only show if a main category is selected
                            <FilterSection title={FILTER_CATEGORIES.typecategory.title}>
                                {FILTER_CATEGORIES.typecategory.options.map(option => (
                                    <FilterItem
                                        key={option.value}
                                        label={option.label}
                                        value={option.value}
                                        checked={filters.typecategory.includes(option.value)}
                                        onChange={() => updateFilter('typecategory', option.value)}
                                    >
                                        {/* Render first level sub-filters (e.g., White coat, Scrubs) if parent is checked */}
                                        {filters.typecategory.includes(option.value) && getTypeCategorySubFilters(option.value).length > 0 && (
                                            <div className="pl-4">
                                                {getTypeCategorySubFilters(option.value).map(subOption => (
                                                    <FilterItem
                                                        key={subOption.value}
                                                        label={subOption.label}
                                                        value={subOption.value}
                                                        checked={filters.subcategory1.includes(subOption.value)}
                                                        onChange={() => updateFilter('subcategory1', subOption.value)}
                                                    />
                                                ))}
                                            </div>
                                        )}
                                    </FilterItem>
                                ))}
                            </FilterSection>
                        )}

                        {/* Subcategory Filter (e.g., White coat, Scrubs) */}
                        {filters.subcategory1.length > 0 && ( // Only show if a type category is selected
                            <FilterSection title={FILTER_CATEGORIES.subcategory1.title}>
                                {FILTER_CATEGORIES.subcategory1.options.map(option => (
                                    <FilterItem
                                        key={option.value}
                                        label={option.label}
                                        value={option.value}
                                        checked={filters.subcategory1.includes(option.value)}
                                        onChange={() => updateFilter('subcategory1', option.value)}
                                    >
                                        {/* Render second level sub-filters (if any) when a subcategory is checked */}
                                        {filters.subcategory1.includes(option.value) && getSubcategory1SubFilters(option.value).length > 0 && (
                                            <div className="pl-4">
                                                {getSubcategory1SubFilters(option.value).map(subOption => (
                                                    <FilterItem
                                                        key={subOption.value}
                                                        label={subOption.label}
                                                        value={subOption.value}
                                                        checked={filters.subcategory2.includes(subOption.value)}
                                                        onChange={() => updateFilter('subcategory2', subOption.value)}
                                                    />
                                                ))}
                                            </div>
                                        )}
                                    </FilterItem>
                                ))}
                            </FilterSection>
                        )}
                    </div>
                </div>

                {/* Products Grid */}
                <div className="flex-1">
                    {/* Sort and View Options (e.g., Grid/List view, Sort by price/relevance) */}
                    <div className="flex flex-col sm:flex-row justify-between items-center py-4">
                        {/* Sort By */}
                        <div className="flex items-center gap-2">
                            <span className="text-sm">Sort by:</span>
                            <select
                                value={sortType}
                                onChange={(e) => setSortType(e.target.value)}
                                className="border border-slate-300 rounded-md p-2 text-sm"
                            >
                                <option value="relavent">Relevance</option>
                                <option value="low-high">Price: Low to High</option>
                                <option value="high-low">Price: High to Low</option>
                            </select>
                        </div>

                        {/* View Options (Grid/List) - Assuming you have a state for view type */}
                        <div className="flex items-center gap-2">
                            <span className="text-sm">View:</span>
                            <button className={`p-2 rounded-md ${viewType === 'grid' ? 'bg-blue-500 text-white' : 'bg-slate-100'}`} onClick={() => setViewType('grid')}>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h18M3 9h18M3 15h18M3 21h18" />
                                </svg>
                            </button>
                            <button className={`p-2 rounded-md ${viewType === 'list' ? 'bg-blue-500 text-white' : 'bg-slate-100'}`} onClick={() => setViewType('list')}>
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7h18M3 12h18M3 17h18" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Products Grid/List - Assuming you have a component for rendering products */}
                    <div className={`grid ${viewType === 'grid' ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'} gap-4`}>
                        {products.map(product => (
                            <ProductItem key={product.id} product={product} />
                        ))}
                    </div>

                    {/* Pagination - Assuming you have a pagination component */}
                    <div className="flex justify-center py-4">
                        <button
                            onClick={() => paginate(paginationResult.currentPage - 1)}
                            disabled={paginationResult.currentPage === 1}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50"
                        >
                            <FaAngleLeft className="w-4 h-4" />
                        </button>
                        <span className="px-4 py-2 text-sm">
                            Page {paginationResult.currentPage} of {paginationResult.numberOfPages}
                        </span>
                        <button
                            onClick={() => paginate(paginationResult.currentPage + 1)}
                            disabled={paginationResult.currentPage === paginationResult.numberOfPages}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md disabled:opacity-50"
                        >
                            <FaAngleRight className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}