import { useContext } from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminNavBar from './AdminNavBar';
import AdminSideBar from './AdminSideBar';
import AddForAdmin from '../pages/AddForAdmin';
import ListForAdmin from '../pages/ListForAdmin';
import OrdersForAdmin from '../pages/OrdersForAdmin';
import CouponsForAdmin from '../pages/CouponsForAdmin';
import UsersForAdmin from '../pages/UsersForAdmin';
import EditProductForAdmin from '../pages/EditProductForAdmin';
import AdminWelcome from './AdminWelcome';
import { ShopContext } from '../../context/ShopContextProvider';
import { ToastContainer } from 'react-toastify';

export default function AdminLayout() {
    const { setToken } = useContext(ShopContext);

    return (
        <div className='bg-[--color2] dark:bg-slate-900 min-h-screen transition-all duration-300'>
            <ToastContainer />
            <AdminNavBar setToken={setToken} />
            <hr className='border-gray-300 dark:border-gray-700' />
            <div className='flex w-full'>
                <AdminSideBar />
                <div className='w-[70%] mx-auto ml-[max(5vw,25px)] my-8 text-gray-600 dark:text-gray-300 text-base'>
                    <Routes>
                        <Route path="/" element={<AdminWelcome />} />
                        <Route path="/add" element={<AddForAdmin />} />
                        <Route path="/list" element={<ListForAdmin />} />
                        <Route path="/orders" element={<OrdersForAdmin />} />
                        <Route path="/coupons" element={<CouponsForAdmin />} />
                        <Route path="/users" element={<UsersForAdmin />} />
                        <Route path="/edit/:id" element={<EditProductForAdmin />} />
                    </Routes>
                </div>
            </div>
        </div>
    );
}
