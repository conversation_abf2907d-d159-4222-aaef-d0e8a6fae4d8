import { useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ShopContext } from '../context/ShopContextProvider';

const AdminRedirect = ({ children }) => {
    const { token, isAdmin } = useContext(ShopContext);
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        // If user is logged in and is admin, redirect to dashboard
        if (token && isAdmin()) {
            // Don't redirect if already on admin routes or login page
            if (!location.pathname.startsWith('/admin') && location.pathname !== '/Login') {
                navigate('/admin', { replace: true });
            }
        }
    }, [token, isAdmin, navigate, location.pathname]);

    // If user is admin and not on admin routes, don't render children
    if (token && isAdmin() && !location.pathname.startsWith('/admin') && location.pathname !== '/Login') {
        return null; // Don't render anything while redirecting
    }

    return children;
};

export default AdminRedirect;
