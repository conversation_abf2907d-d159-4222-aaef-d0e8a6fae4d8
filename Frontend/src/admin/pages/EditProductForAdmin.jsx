import React, { useEffect, useState, useContext } from 'react';
import axios from 'axios';
import { ShopContext } from '../../context/ShopContextProvider';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { assets } from '../assets/assets';

export default function EditProductForAdmin() {
    const { backendUrl, token } = useContext(ShopContext);
    const { id } = useParams();
    const navigate = useNavigate();

    // Form states
    const [imageCover, setImageCover] = useState(null);
    const [images, setImages] = useState([]);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [price, setPrice] = useState('');
    const [quantity, setQuantity] = useState('');
    const [category, setCategory] = useState('Medical');
    const [typecategory, setTypecategory] = useState('Clothes');
    const [subcategory1, setSubcategogy1] = useState('Select a subcategory');
    const [subcategory2, setSubcategogy2] = useState('Select a subcategory details');
    const [sizes, setSizes] = useState([]);
    const [loading, setLoading] = useState(true);

    // Fetch product data
    useEffect(() => {
        async function fetchProduct() {
            try {
                const res = await axios.get(`${backendUrl}/api/v1/products/${id}`);
                const p = res.data.data;
                setName(p.name || '');
                setDescription(p.description || '');
                setPrice(p.price || '');
                setQuantity(p.quantity || '');
                setCategory(p.category || 'Medical');
                setTypecategory(p.typecategory || 'Clothes');
                setSubcategogy1(p.subcategory1 || 'Select a subcategory');
                setSubcategogy2(p.subcategory2 || 'Select a subcategory details');
                setSizes(Array.isArray(p.sizes) ? p.sizes : []);
                setImageCover(p.imageCover || null);
                setImages(Array.isArray(p.images) ? p.images : []);
            } catch (err) {
                toast.error("Failed to fetch product");
            }
            setLoading(false);
        }
        fetchProduct();
    }, [id, backendUrl]);

    // Handle image uploads
    const handleImageUpload = (e) => {
        const files = Array.from(e.target.files);
        setImages(prev => [...prev, ...files]);
    };
    const removeImage = (index) => {
        setImages(prev => prev.filter((_, i) => i !== index));
    };

    // Handle cover image
    const handleCoverImage = (e) => {
        setImageCover(e.target.files[0]);
    };

    // Handle update
    async function handleUpdate(e) {
        e.preventDefault();
        try {
            const formData = new FormData();
            formData.append('name', name);
            formData.append('description', description);
            formData.append('price', price);
            formData.append('quantity', quantity);
            formData.append('category', category);
            formData.append('typecategory', typecategory);
            formData.append('subcategory1', subcategory1);
            formData.append('subcategory2', subcategory2);
            sizes.forEach(size => formData.append('sizes', size));
            // Only append new images (File objects)
            images.forEach(img => {
                if (typeof img === 'string') return; // skip existing URLs
                formData.append('images', img);
            });
            if (imageCover && typeof imageCover !== 'string') {
                formData.append('imageCover', imageCover);
            }
            // If no new images/cover, backend should keep old ones

            const response = await axios.put(
                `${backendUrl}/api/v1/products/${id}`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            toast.success("Product updated!");
            navigate('/admin/list');
        } catch (error) {
            toast.error(error.response?.data?.message || error.message);
        }
    }

    if (loading) return <div className="text-center py-10">Loading...</div>;

    return (
        <form onSubmit={handleUpdate} className='flex flex-col w-full items-start gap-3 max-w-3xl mx-auto p-6 bg-white dark:bg-slate-800 rounded-xl shadow-md'>
            <h2 className="text-2xl font-bold mb-2 text-[#263D54] dark:text-white">Edit Product</h2>
            {/* Cover Image */}
            <div className='flex flex-col gap-1'>
                <p className='mb-1 text-gray-700 dark:text-gray-300'>Cover Image</p>
                <label className='cursor-pointer' htmlFor='imageCover'>
                    <img
                        className='w-20 h-20 object-cover rounded border'
                        src={
                            imageCover
                                ? typeof imageCover === 'string'
                                    ? imageCover
                                    : URL.createObjectURL(imageCover)
                                : assets.upload_area
                        }
                        alt=""
                    />
                    <input onChange={handleCoverImage} type="file" id='imageCover' hidden />
                </label>
            </div>
            {/* Product Images */}
            <div className='flex flex-col gap-1'>
                <p className='mb-1 text-gray-700 dark:text-gray-300'>Upload Images</p>
                <div className='flex flex-wrap gap-2'>
                    {images.map((img, index) => (
                        <div key={index} className="relative">
                            <img
                                className="w-32 h-32 object-cover rounded-lg border border-gray-300 shadow-sm"
                                src={typeof img === 'string' ? img : URL.createObjectURL(img)}
                                alt={`Preview ${index}`}
                                style={{ maxWidth: '128px', maxHeight: '128px' }}
                            />
                            <span className="absolute bottom-1 left-1 bg-white bg-opacity-80 text-xs px-2 py-0.5 rounded shadow">
                                128x128px
                            </span>
                            <button
                                type="button"
                                className="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
                                onClick={() => removeImage(index)}
                            >
                                ×
                            </button>
                        </div>
                    ))}
                    <label className='cursor-pointer' htmlFor='images'>
                        <img className='w-20' src={assets.upload_area} alt="" />
                        <input
                            onChange={handleImageUpload}
                            type="file"
                            id='images'
                            multiple
                            hidden
                        />
                    </label>
                </div>
            </div>
            {/* Product Name */}
            <div className='w-full'>
                <p className='mb-2 text-gray-700 dark:text-gray-300'>Product Name:</p>
                <input onChange={(e) => setName(e.target.value)} value={name} required type="text" className='w-full max-w-[500px] px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500' placeholder='Type here' />
            </div>
            {/* Product Description */}
            <div className='w-full'>
                <p className='mb-2 text-gray-700 dark:text-gray-300'>Product Description:</p>
                <textarea onChange={(e) => setDescription(e.target.value)} value={description} required className='w-full max-w-[500px] px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500' placeholder='Write content here' />
            </div>
            {/* Category, Type, Subcategory, etc. */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full'>
                <div>
                    <p className='mb-2 text-gray-700 dark:text-gray-300'>Product Category:</p>
                    <select onChange={(e) => setCategory(e.target.value)} value={category} required className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500'>
                        <option value="Medical">Medical</option>
                        <option value="Geometric">Geometric</option>
                    </select>
                </div>
                <div>
                    <p className='mb-2 text-gray-700 dark:text-gray-300'>Type:</p>
                    <select onChange={(e) => setTypecategory(e.target.value)} value={typecategory} required className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500'>
                        <option value={"Clothes"}>Clothes</option>
                        <option value="Tools">Tools</option>
                    </select>
                </div>
                <div>
                    <p className='mb-2 text-gray-700 dark:text-gray-300'>Subcategory 1:</p>
                    <select onChange={(e) => setSubcategogy1(e.target.value)} value={subcategory1} required className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500'>
                        <option value="Select a subcategory">Select a subcategory</option>
                        {/* Add your subcategory options here */}
                    </select>
                </div>
                <div>
                    <p className='mb-2 text-gray-700 dark:text-gray-300'>Subcategory 2:</p>
                    <select onChange={(e) => setSubcategogy2(e.target.value)} value={subcategory2} required className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-white rounded focus:outline-none focus:ring-2 focus:ring-blue-500'>
                        <option value="Select a subcategory details">Select a subcategory details</option>
                        {/* Add your subcategory options here */}
                    </select>
                </div>
            </div>
            {/* Sizes (copy your logic from AddForAdmin.jsx) */}
            {/* ... */}
            {/* Price & Quantity */}
            <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 w-full'>
                <div>
                    <p className='mb-2'>Product Price:</p>
                    <input onChange={(e) => setPrice(e.target.value)} value={price} className='w-full px-3 py-2 sm:w-[120px]' type="Number" placeholder='25' />
                </div>
                <div>
                    <p className='mb-2'>Product Quantity:</p>
                    <input onChange={(e) => setQuantity(e.target.value)} value={quantity} className='w-full px-3 py-2 sm:w-[120px]' type="Number" placeholder='1' />
                </div>
            </div>
            {/* Sizes, Bestseller, etc. (copy from AddForAdmin.jsx as needed) */}
            <button type='submit' className='px-8 py-3 mt-4 border border-black dark:border-gray-600 text-sm hover:bg-black hover:text-white dark:hover:bg-slate-600 transition-all duration-500 text-black dark:text-white bg-white dark:bg-slate-700 active:bg-gray-800 dark:active:bg-slate-500 rounded'>
                Update Product
            </button>
        </form>
    );
}