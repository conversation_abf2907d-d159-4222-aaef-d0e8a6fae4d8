import { NavLink, useLocation } from 'react-router-dom';
import { assets } from '../assets/assets';

export default function AdminSideBar() {
    const location = useLocation();

    // Determine which link should be active based on the current pathname
    const getActive = (key) => {
        if (location.pathname === '/admin/') return false; // Unactive all on dashboard
        if (key === 'add' && location.pathname.startsWith('/admin/add')) return true;
        if (key === 'list' && location.pathname.startsWith('/admin/list')) return true;
        if (key === 'orders' && location.pathname.startsWith('/admin/orders')) return true;
        if (key === 'coupons' && location.pathname.startsWith('/admin/coupons')) return true;
        if (key === 'users' && location.pathname.startsWith('/admin/users')) return true;
        return false;
    };

    return (
        <div className='w-full md:w-[15%] min-h-screen border-r border-gray-700 dark:border-gray-600 bg-[--color1] dark:bg-slate-800 text-white flex flex-col justify-between transition-all duration-300'>
            {/* Top section: Navigation Links */}
            <div className='flex flex-col gap-2 pt-6 px-4 md:pl-[15%]'> {/* Adjusted padding for better responsiveness */}

                {/* NavLink for Add Items */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('add')
                            ? 'bg-[#2b5175] dark:bg-slate-600 text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 dark:bg-slate-700 hover:bg-gray-700 dark:hover:bg-slate-600 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/add'}
                >
                    {/* Hover effect background */}
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.add_icon} alt="" />
                    <p className='hidden md:block font-medium'>Add Items</p>
                </NavLink>

                {/* NavLink for List Items */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('list')
                            ? 'bg-[#2b5175] dark:bg-slate-600 text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 dark:bg-slate-700 hover:bg-gray-700 dark:hover:bg-slate-600 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/list'}
                >
                     <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.order_icon} alt="" />
                    <p className='hidden md:block font-medium'>List Items</p>
                </NavLink>

                {/* NavLink for Orders */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('orders')
                            ? 'bg-[#2b5175] dark:bg-slate-600 text-white shadow-lg transform scale-105' // Changed to match logout button color
                            : 'bg-gray-800 dark:bg-slate-700 hover:bg-gray-700 dark:hover:bg-slate-600 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/orders'}
                >
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <img className='w-5 h-5 filter invert group-hover:filter-none transition-all duration-300' src={assets.order_icon} alt="" />
                    <p className='hidden md:block font-medium'>Orders</p>
                </NavLink>

                {/* NavLink for Coupons */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('coupons')
                            ? 'bg-[#263D54] dark:bg-slate-600 text-white shadow-lg transform scale-105'
                            : 'bg-gray-800 dark:bg-slate-700 hover:bg-gray-700 dark:hover:bg-slate-600 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/coupons'}
                >
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="3" y="7" width="18" height="10" rx="2" /><path d="M7 7v10M17 7v10" /></svg>
                    <p className='hidden md:block font-medium'>Coupons</p>
                </NavLink>

                {/* NavLink for Users */}
                <NavLink
                    className={() =>
                        `flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 ease-in-out
                        ${getActive('users')
                            ? 'bg-[#263D54] dark:bg-slate-600 text-white shadow-lg transform scale-105'
                            : 'bg-gray-800 dark:bg-slate-700 hover:bg-gray-700 dark:hover:bg-slate-600 text-gray-300 hover:text-white'
                        }
                        group relative overflow-hidden`
                    }
                    to={'/admin/users'}
                >
                    <span className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="8" r="4"/><path d="M6 20v-2a4 4 0 014-4h0a4 4 0 014 4v2"/></svg>
                    <p className='hidden md:block font-medium'>Users</p>
                </NavLink>
            </div>
        </div>
    );
}

